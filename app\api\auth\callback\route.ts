import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get("code")

  // Debug logging
  console.log('=== CALLBACK DEBUG INFO ===')
  console.log('request.url:', request.url)
  console.log('request.nextUrl.origin:', request.nextUrl.origin)
  console.log('searchParams:', Object.fromEntries(searchParams.entries()))
  console.log('code:', code)
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)

  if (!code) {
    const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'
    const redirectUrl = `${baseUrl}/?error=no_code`
    console.log('No code found, redirecting to:', redirectUrl)
    console.log('========================')
    return NextResponse.redirect(redirectUrl)
  }

  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'
    const redirectUri = `${baseUrl}/api/auth/callback`
    const tokenData = await KeycloakService.exchangeCodeForToken(code, redirectUri)
    const userInfo = await KeycloakService.getUserInfo(tokenData.access_token)

    // Set secure cookies
    const cookieStore = cookies()
    cookieStore.set("access_token", tokenData.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    })

    cookieStore.set("refresh_token", tokenData.refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.refresh_expires_in,
    })

    cookieStore.set("user_info", JSON.stringify(userInfo), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    })

    const baseUrl = process.env.NEXTAUTH_URL || request.nextUrl.origin
    const successUrl = `${baseUrl}/`
    console.log('Auth success, redirecting to:', successUrl)
    console.log('========================')
    return NextResponse.redirect(successUrl)
  } catch (error) {
    console.error("Auth callback error:", error)
    const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'
    const errorUrl = `${baseUrl}/?error=auth_failed`
    console.log('Auth failed, redirecting to:', errorUrl)
    console.log('========================')
    return NextResponse.redirect(errorUrl)
  }
}
