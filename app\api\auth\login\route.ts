import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"

export async function GET(request: NextRequest) {
  // 强制使用环境变量，不依赖request.nextUrl.origin
  const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'
  const redirectUri = `${baseUrl}/api/auth/callback`

  // Enhanced debug logging
  console.log('=== LOGIN DEBUG INFO ===')
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)
  console.log('request.nextUrl.origin:', request.nextUrl.origin)
  console.log('request.url:', request.url)
  console.log('Final baseUrl:', baseUrl)
  console.log('Final redirectUri:', redirectUri)
  console.log('KEYCLOAK_URL:', process.env.KEYCLOAK_URL)
  console.log('KEYCLOAK_REALM:', process.env.KEYCLOAK_REALM)
  console.log('KEYCLOAK_CLIENT_ID:', process.env.KEYCLOAK_CLIENT_ID)

  const loginUrl = KeycloakService.getLoginUrl(redirectUri)
  console.log('Generated loginUrl:', loginUrl)
  console.log('========================')

  // 正常重定向到Keycloak
  return NextResponse.redirect(loginUrl)
}
