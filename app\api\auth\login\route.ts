import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXTAUTH_URL || request.nextUrl.origin
  const redirectUri = `${baseUrl}/api/auth/callback`

  // Debug logging
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)
  console.log('request.nextUrl.origin:', request.nextUrl.origin)
  console.log('Final baseUrl:', baseUrl)
  console.log('Final redirectUri:', redirectUri)

  const loginUrl = KeycloakService.getLoginUrl(redirectUri)
  console.log('Generated loginUrl:', loginUrl)

  return NextResponse.redirect(loginUrl)
}
