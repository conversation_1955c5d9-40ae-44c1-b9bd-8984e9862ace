import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXTAUTH_URL || request.nextUrl.origin
  const redirectUri = `${baseUrl}/api/auth/callback`

  // Enhanced debug logging
  console.log('=== LOGIN DEBUG INFO ===')
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)
  console.log('request.nextUrl.origin:', request.nextUrl.origin)
  console.log('request.url:', request.url)
  console.log('Final baseUrl:', baseUrl)
  console.log('Final redirectUri:', redirectUri)
  console.log('KEYCLOAK_URL:', process.env.KEYCLOAK_URL)
  console.log('KEYCLOAK_REALM:', process.env.KEYCLOAK_REALM)
  console.log('KEYCLOAK_CLIENT_ID:', process.env.KEYCLOAK_CLIENT_ID)

  const loginUrl = KeycloakService.getLoginUrl(redirectUri)
  console.log('Generated loginUrl:', loginUrl)
  console.log('========================')

  // 临时返回JSON用于调试，而不是重定向
  return NextResponse.json({
    debug: {
      baseUrl,
      redirectUri,
      loginUrl,
      env: {
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        KEYCLOAK_URL: process.env.KEYCLOAK_URL,
        KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
        KEYCLOAK_CLIENT_ID: process.env.KEYCLOAK_CLIENT_ID
      }
    }
  })

  // 正常情况下应该是这行：
  // return NextResponse.redirect(loginUrl)
}
