import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { KeycloakService } from "@/app/lib/keycloak"

export async function POST(request: NextRequest) {
  const cookieStore = cookies()
  const refreshToken = cookieStore.get("refresh_token")

  if (refreshToken) {
    try {
      await KeycloakService.logout(refreshToken.value)
    } catch (error) {
      console.error("Keycloak logout error:", error)
    }
  }

  // Clear all auth cookies
  cookieStore.delete("access_token")
  cookieStore.delete("refresh_token")
  cookieStore.delete("user_info")

  return NextResponse.json({ success: true })
}
