import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
  }

  try {
    const user = JSON.parse(userInfo.value)
    return NextResponse.json(user)
  } catch (error) {
    return NextResponse.json({ error: "Invalid user data" }, { status: 401 })
  }
}
