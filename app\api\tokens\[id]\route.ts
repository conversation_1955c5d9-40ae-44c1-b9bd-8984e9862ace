import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { TokenService } from "@/app/lib/db"

async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const tokenData = await request.json()
    const success = TokenService.updateToken(params.id, tokenData, user.id)

    if (!success) {
      return NextResponse.json({ error: "Token not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to update token:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const success = TokenService.deleteToken(params.id, user.id)

    if (!success) {
      return NextResponse.json({ error: "Token not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to delete token:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
