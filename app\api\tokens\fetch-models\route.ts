import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const { urlPath, token } = await request.json()

    if (!urlPath || !token) {
      return NextResponse.json({
        error: "URL path and token are required",
        canFetch: false
      }, { status: 400 })
    }

    // Construct the API URL - ensure it's a complete URL
    let apiUrl = urlPath.trim()

    // Add protocol if missing
    if (!apiUrl.startsWith('http://') && !apiUrl.startsWith('https://')) {
      apiUrl = `https://${apiUrl}`
    }

    // Remove trailing slash if present
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1)
    }

    // Add the models endpoint
    apiUrl = `${apiUrl}/v1/models`

    console.log(`Attempting to fetch models from: ${apiUrl}`)
    console.log(`Using token: ${token.substring(0, 10)}...`)

    // Make the request to fetch models
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000) // 10 seconds timeout
    })

    console.log(`Response status: ${response.status} ${response.statusText}`)
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()))

    if (response.ok) {
      // Check if response is JSON
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text()
        console.log(`Non-JSON response received:`, textResponse.substring(0, 200))
        return NextResponse.json({
          success: false,
          canFetch: false,
          error: `API returned non-JSON response. Content-Type: ${contentType}`,
          models: []
        }, { status: 200 })
      }

      const data = await response.json()

      // Extract model IDs from the response
      let modelIds: string[] = []

      if (data && data.data && Array.isArray(data.data)) {
        modelIds = data.data
          .filter((item: any) => item && item.id)
          .map((item: any) => item.id)
      }

      return NextResponse.json({
        success: true,
        canFetch: true,
        models: modelIds,
        message: `Successfully fetched ${modelIds.length} models`
      })
    } else {
      // Handle different error status codes
      let errorMessage = `Failed to fetch models: ${response.status} ${response.statusText}`

      try {
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json()
          if (errorData.error) {
            errorMessage = errorData.error
          }
        } else {
          const textResponse = await response.text()
          console.log(`Error response (non-JSON):`, textResponse.substring(0, 200))
          errorMessage = `API returned ${response.status} with non-JSON response`
        }
      } catch (e) {
        console.log(`Error parsing error response:`, e)
        // If we can't parse the error response, use the default message
      }

      return NextResponse.json({
        success: false,
        canFetch: false,
        error: errorMessage,
        models: []
      }, { status: 200 }) // Return 200 so frontend can handle the error gracefully
    }
  } catch (error) {
    console.error("Error fetching models:", error)
    console.error("Error details:", {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })

    let errorMessage = "Failed to fetch models"

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        errorMessage = "Request timeout - the API took too long to respond"
      } else if (error.message.includes('fetch')) {
        errorMessage = "Network error - unable to connect to the API"
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = "DNS resolution failed - check if the URL is correct"
      } else if (error.message.includes('ECONNREFUSED')) {
        errorMessage = "Connection refused - the server is not responding"
      } else {
        errorMessage = error.message
      }
    }

    return NextResponse.json({
      success: false,
      canFetch: false,
      error: errorMessage,
      models: []
    }, { status: 200 }) // Return 200 so frontend can handle the error gracefully
  }
}
