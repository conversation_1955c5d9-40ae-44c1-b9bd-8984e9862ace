/**
 * API utility functions with automatic authentication handling
 */

interface ApiRequestOptions extends RequestInit {
  redirectOnUnauthorized?: boolean;
}

/**
 * Enhanced fetch function that automatically handles 401 errors
 */
export async function apiRequest(
  url: string,
  options: ApiRequestOptions = {}
): Promise<Response> {
  const { redirectOnUnauthorized = true, ...fetchOptions } = options;

  try {
    const response = await fetch(url, fetchOptions);

    // Handle unauthorized responses
    if (response.status === 401 && redirectOnUnauthorized) {
      // Redirect to login page
      window.location.href = "/api/auth/login";
      throw new Error("Unauthorized - redirecting to login");
    }

    return response;
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
}

/**
 * Convenience function for GET requests
 */
export async function apiGet(url: string, options?: ApiRequestOptions): Promise<Response> {
  return apiRequest(url, { ...options, method: "GET" });
}

/**
 * Convenience function for POST requests
 */
export async function apiPost(
  url: string,
  data?: any,
  options?: ApiRequestOptions
): Promise<Response> {
  return apiRequest(url, {
    ...options,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Convenience function for PUT requests
 */
export async function apiPut(
  url: string,
  data?: any,
  options?: ApiRequestOptions
): Promise<Response> {
  return apiRequest(url, {
    ...options,
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Convenience function for DELETE requests
 */
export async function apiDelete(url: string, options?: ApiRequestOptions): Promise<Response> {
  return apiRequest(url, { ...options, method: "DELETE" });
}
