export interface KeycloakUser {
  id: string
  name: string
  email: string
  preferred_username: string
}

export class KeycloakService {
  private static readonly KEYCLOAK_URL = process.env.KEYCLOAK_URL!
  private static readonly REALM = process.env.KEYCLOAK_REALM!
  private static readonly CLIENT_ID = process.env.K<PERSON>YCLOAK_CLIENT_ID!
  private static readonly CLIENT_SECRET = process.env.KEYCLOAK_CLIENT_SECRET!

  static getLoginUrl(redirectUri: string): string {
    const params = new URLSearchParams({
      client_id: this.CLIENT_ID,
      redirect_uri: redirectUri,
      response_type: "code",
      scope: "openid profile email",
    })

    return `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/auth?${params}`
  }

  static async exchangeCodeForToken(code: string, redirectUri: string) {
    const tokenUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/token`

    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        code,
        redirect_uri: redirectUri,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to exchange code for token")
    }

    return response.json()
  }

  static async getUserInfo(accessToken: string): Promise<KeycloakUser> {
    const userInfoUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/userinfo`

    const response = await fetch(userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!response.ok) {
      throw new Error("Failed to get user info")
    }

    const userInfo = await response.json()

    return {
      id: userInfo.sub,
      name: userInfo.name || userInfo.preferred_username,
      email: userInfo.email,
      preferred_username: userInfo.preferred_username,
    }
  }

  static async logout(refreshToken: string): Promise<void> {
    const logoutUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/logout`

    await fetch(logoutUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        refresh_token: refreshToken,
      }),
    })
  }
}
