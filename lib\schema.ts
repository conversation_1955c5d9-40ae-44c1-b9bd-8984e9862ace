import { sqliteTable, text } from 'drizzle-orm/sqlite-core';
import { createId } from '@paralleldrive/cuid2';

export const tokens = sqliteTable('tokens', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  name: text('name').notNull(),
  urlPath: text('url_path'),
  apiPath: text('api_path'),
  token: text('token').notNull(),
  modelList: text('model_list'), // JSON string
  note: text('note'),
  createdAt: text('created_at').$defaultFn(() => new Date().toISOString()),
  updatedAt: text('updated_at').$defaultFn(() => new Date().toISOString()),
});

export type Token = typeof tokens.$inferSelect;
export type NewToken = typeof tokens.$inferInsert;
