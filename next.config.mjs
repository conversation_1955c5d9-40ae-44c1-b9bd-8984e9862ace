/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // 添加反向代理支持
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Forwarded-Host',
            value: 'token-library.techexpresser.com',
          },
        ],
      },
    ]
  },
  // Uncomment the following line if you want to disable React Strict Mode
  // to avoid double API calls in development (NOT recommended)
  // reactStrictMode: false,
}

export default nextConfig
