<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h2>生成带背景的图标</h2>
    <canvas id="iconCanvas" width="512" height="512" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">下载图标</button>
    
    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制浅色背景
        ctx.fillStyle = '#f8fafc'; // 浅灰蓝色背景
        ctx.fillRect(0, 0, 512, 512);
        
        // 添加圆角
        ctx.beginPath();
        ctx.roundRect(0, 0, 512, 512, 64);
        ctx.fillStyle = '#f1f5f9'; // 稍微更浅的背景
        ctx.fill();
        
        // 绘制主要图标区域
        ctx.beginPath();
        ctx.roundRect(64, 64, 384, 384, 48);
        ctx.fillStyle = '#3b82f6'; // 蓝色主色调
        ctx.fill();
        
        // 绘制内部区域
        ctx.beginPath();
        ctx.roundRect(96, 96, 320, 320, 32);
        ctx.fillStyle = '#ffffff';
        ctx.fill();
        
        // 绘制Token符号
        ctx.beginPath();
        ctx.arc(256, 256, 96, 0, 2 * Math.PI);
        ctx.fillStyle = '#3b82f6';
        ctx.fill();
        
        // 绘制字母T
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 120px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('T', 256, 256);
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'token-manager-icon-with-background.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
